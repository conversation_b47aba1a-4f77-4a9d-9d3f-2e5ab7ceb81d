/* Red Form Style for RID COD Plugin - Matching Red Design (Works on all devices) */

/* Red Form Container - Red rounded design (responsive for all devices) */
.rid-cod-form-mobile #rid-cod-checkout {
    background: #ffffff;
    padding: 20px;
    border-radius: 15px;
    border: 2px solid var(--rid-cod-accent-color);
    box-shadow: 0 4px 20px rgba(106, 61, 232, 0.1);
    margin-bottom: 20px;
    max-width: 100%;
    margin-bottom: 30px;
    font-family: 'Cairo', 'Taja<PERSON>', Arial, sans-serif;
    direction: rtl;
    position: relative;
}

/* Title matching mobile design */
.rid-cod-form-mobile .rid-cod-title h3 {
    font-size: 16px;
    margin-bottom: 20px;
    text-align: center;
    color: #2c3e50;
    font-weight: 600;
    line-height: 1.4;
    padding: 0;
}

/* Form layout for mobile */
.rid-cod-form-mobile #rid-cod-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.rid-cod-form-mobile .rid-cod-customer-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

/* Input Fields with icons matching mobile design */
.rid-cod-form-mobile #rid-cod-form input[type="text"],
.rid-cod-form-mobile #rid-cod-form input[type="tel"],
.rid-cod-form-mobile #rid-cod-form input[type="email"],
.rid-cod-form-mobile #rid-cod-form select,
.rid-cod-form-mobile #rid-cod-form textarea {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid var(--rid-cod-border-color);
    border-radius: 10px;
    font-size: 14px;
    color: #2c3e50;
    background: #ffffff;
    transition: all 0.2s ease;
    box-shadow: none;
    height: auto;
    min-height: 45px;
    font-weight: 400;
}

.rid-cod-form-mobile #rid-cod-form input:focus,
.rid-cod-form-mobile #rid-cod-form select:focus,
.rid-cod-form-mobile #rid-cod-form textarea:focus {
    outline: none;
    border-color: var(--rid-cod-accent-color);
    box-shadow: 0 0 0 3px rgba(106, 61, 232, 0.1);
    background: #ffffff;
}

.rid-cod-form-mobile #rid-cod-form input::placeholder,
.rid-cod-form-mobile #rid-cod-form select::placeholder,
.rid-cod-form-mobile #rid-cod-form textarea::placeholder {
    color: #95a5a6;
    font-weight: 400;
}

/* Field Groups with Icons */
.rid-cod-form-mobile .rid-cod-field-group {
    position: relative;
    margin-bottom: 0;
}

.rid-cod-form-mobile .rid-cod-field-with-icon {
    position: relative;
}

/* Icons styling for mobile */
.rid-cod-form-mobile .rid-input-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    z-index: 2;
    pointer-events: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: #ffffff;
    font-weight: bold;
}

/* Icon colors matching mobile design */
.rid-cod-form-mobile .rid-input-icon.rid-icon-user {
    background: #3498db;
}

.rid-cod-form-mobile .rid-input-icon.rid-icon-phone {
    background: #27ae60;
}

.rid-cod-form-mobile .rid-input-icon.rid-icon-state,
.rid-cod-form-mobile .rid-input-icon.rid-icon-city {
    background: #f39c12;
}

.rid-cod-form-mobile .rid-input-icon::before {
    content: '👤';
}

.rid-cod-form-mobile .rid-input-icon.rid-icon-phone::before {
    content: '📞';
}

.rid-cod-form-mobile .rid-input-icon.rid-icon-state::before,
.rid-cod-form-mobile .rid-input-icon.rid-icon-city::before {
    content: '🏠';
}

/* Quantity selector matching mobile design */
.rid-cod-form-mobile .rid-cod-quantity-selector {
    display: flex;
    align-items: center;
    background: #ffffff;
    border: 2px solid var(--rid-cod-border-color);
    border-radius: 10px;
    overflow: hidden;
    margin: 15px 0;
    max-width: 150px;
    margin-left: auto;
    margin-right: auto;
}

.rid-cod-form-mobile .rid-cod-quantity-selector button {
    background: #f8f9fa;
    border: none;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    cursor: pointer;
    transition: all 0.2s ease;
}

.rid-cod-form-mobile .rid-cod-quantity-selector button:hover {
    background: #e9ecef;
}

.rid-cod-form-mobile .rid-cod-quantity-selector input {
    border: none;
    width: 60px;
    height: 40px;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    background: #ffffff;
    margin: 0;
    padding: 0;
    min-height: auto;
}

.rid-cod-form-mobile .rid-cod-quantity-selector input:focus {
    outline: none;
    box-shadow: none;
    border-color: transparent;
}

/* Submit button matching mobile design */
.rid-cod-form-mobile #rid-cod-submit-btn {
    background: var(--rid-cod-button-bg-color);
    color: var(--rid-cod-button-text-color);
    border: none;
    padding: 15px 25px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 15px rgba(106, 61, 232, 0.3);
    position: relative;
    overflow: hidden;
    min-height: 50px;
    width: 100%;
    margin-top: 10px;
}

.rid-cod-form-mobile #rid-cod-submit-btn::after {
    content: '👆';
    margin-right: 8px;
}

.rid-cod-form-mobile #rid-cod-submit-btn:hover {
    background: var(--rid-cod-accent-color);
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(106, 61, 232, 0.4);
}

.rid-cod-form-mobile #rid-cod-submit-btn:active {
    transform: translateY(0);
}

/* Order summary matching mobile design */
.rid-cod-form-mobile #rid-cod-summary-wrapper {
    background: #f8f9fa;
    border: 2px solid var(--rid-cod-accent-color);
    border-radius: 12px;
    margin-top: 20px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(106, 61, 232, 0.1);
}

.rid-cod-form-mobile #rid-cod-summary-header {
    background: #ffffff;
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}

.rid-cod-form-mobile #rid-cod-summary-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
}

.rid-cod-form-mobile #rid-cod-summary-content {
    padding: 15px;
    background: #ffffff;
}

.rid-cod-form-mobile #rid-cod-summary-content table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.rid-cod-form-mobile #rid-cod-summary-content td {
    padding: 6px 0;
    border-bottom: 1px dotted #dee2e6;
    font-size: 13px;
    color: #2c3e50;
}

.rid-cod-form-mobile #rid-cod-summary-content tr:last-child td {
    border-bottom: none;
    font-weight: 700;
    font-size: 14px;
    color: var(--rid-cod-accent-color);
    padding-top: 10px;
}

/* Responsive Design for mobile */
@media (max-width: 768px) {
    .rid-cod-form-mobile #rid-cod-checkout {
        padding: 15px;
        border-radius: 12px;
        margin: 0 10px 15px;
        border-width: 2px;
    }

    .rid-cod-form-mobile .rid-cod-title h3 {
        font-size: 14px;
    }

    .rid-cod-form-mobile .rid-cod-customer-info {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .rid-cod-form-mobile #rid-cod-form input[type="text"],
    .rid-cod-form-mobile #rid-cod-form input[type="tel"],
    .rid-cod-form-mobile #rid-cod-form input[type="email"],
    .rid-cod-form-mobile #rid-cod-form select {
        min-height: 42px;
        font-size: 13px;
    }

    .rid-cod-form-mobile #rid-cod-submit-btn {
        font-size: 15px;
        padding: 12px 20px;
        min-height: 45px;
    }
}

/* Variations section for mobile style - keeping all functionality */
.rid-cod-form-mobile .rid-cod-variations {
    background: transparent;
    border: none;
    border-radius: 0;
    padding: 15px 0;
    margin-bottom: 15px;
    box-shadow: none;
}

.rid-cod-form-mobile .rid-cod-variations::before {
    display: none;
}

/* Variation layout for mobile */
.rid-cod-form-mobile .variation-boxes {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
    justify-content: flex-end;
    align-items: center;
}

.rid-cod-form-mobile .variation-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.rid-cod-form-mobile .variation-label .attribute-label {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0;
    margin-left: 10px;
}

/* Size options for mobile */
.rid-cod-form-mobile .variation-option {
    padding: 8px 16px;
    border: 2px solid var(--rid-cod-border-color);
    border-radius: 8px;
    background: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 600;
    color: #2c3e50;
    min-width: 40px;
    text-align: center;
    position: relative;
    overflow: hidden;
    font-size: 14px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rid-cod-form-mobile .variation-option:hover {
    border-color: var(--rid-cod-accent-color);
    background: #fff5f5;
}

/* Selected state for mobile - red border */
.rid-cod-form-mobile .variation-option.selected {
    border-color: var(--rid-cod-accent-color);
    background: #fff5f5;
    color: var(--rid-cod-accent-color);
    border-width: 3px;
}

/* Color swatches for mobile */
.rid-cod-form-mobile .variation-option.color-option {
    width: 32px;
    height: 32px;
    padding: 0;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--rid-cod-border-color);
    min-width: 32px;
}

.rid-cod-form-mobile .color-swatch {
    width: 100%;
    height: 100%;
    border-radius: 4px;
    border: none;
}

.rid-cod-form-mobile .variation-option.color-option.selected {
    border-color: var(--rid-cod-accent-color);
    border-width: 3px;
}

/* Hide original select elements but keep them in DOM for form submission */
.rid-cod-form-mobile .rid-cod-variations .variations select {
    position: absolute;
    opacity: 0;
    height: 0;
    width: 0;
    pointer-events: none;
}

/* Actions row for mobile */
.rid-cod-form-mobile .rid-cod-actions-row {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 15px;
}

.rid-cod-form-mobile .rid-cod-submit {
    order: 2;
}

.rid-cod-form-mobile .rid-cod-quantity {
    order: 1;
    display: flex;
    justify-content: center;
}
